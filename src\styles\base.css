/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  
  /* Friendly color palette */
  --primary-color: #4CAF50;      /* Friendly green */
  --secondary-color: #2196F3;    /* Calm blue */
  --accent-color: #FF9800;       /* Warm orange */
  --text-primary: #2C2C2C;       /* Soft black, easy on eyes */
  --text-secondary: #5E81AC;     /* Muted blue */
  --background: #F0F0F0;         /* Slightly darker gray-white */
  --surface: #FFFFFF;            /* Pure white */
  --border: #E0E0E0;             /* Light border */
  --shadow: rgba(0, 0, 0, 0.1);  /* Subtle shadow */
}

body {
  margin: 0;
  background-color: var(--background);
  color: var(--text-primary);
  min-height: 100vh;
  font-size: 16px;
}

#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
