import { BaseView } from './BaseView.js'

export class TestView extends BaseView {
  constructor(container) {
    super(container)
    this.onMicClick = null
  }

  render() {
    this.element = this.createElement('div', 'test-view')

    // Main content container
    const content = this.createElement('div', 'test-content')

    // Text passage (floating without container)
    const textPassage = this.createElement('div', 'test-passage',
      'I went to the store to buy some groceries. The store was busy, and there was a long line at the checkout. I still managed to get everything I needed before going home.')

    // Mic button container (positioned at bottom center, raised up)
    const micContainer = this.createElement('div', 'mic-container')
    const micButton = this.createElement('button', 'mic-button test-mic')
    micButton.innerHTML = `
      <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
      </svg>
    `

    // Add click event
    micButton.addEventListener('click', () => {
      if (this.onMicClick) {
        this.onMicClick()
      }
    })

    // Assemble the view
    micContainer.appendChild(micButton)
    content.appendChild(textPassage)

    this.element.appendChild(content)
    this.element.appendChild(micContainer)

    // Add to container
    this.container.innerHTML = ''
    this.container.appendChild(this.element)

    return this.element
  }

  setMicClickHandler(handler) {
    this.onMicClick = handler
  }

}
