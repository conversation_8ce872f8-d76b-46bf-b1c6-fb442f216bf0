import { BaseModel } from './BaseModel.js'

export class LandingModel extends BaseModel {
  constructor() {
    super()
    this.setData('title', 'Wanna test how good your speaking skill is?')
    this.setData('micActive', false)
  }

  activateMic() {
    this.setData('micActive', true)
  }

  deactivateMic() {
    this.setData('micActive', false)
  }

  isMicActive() {
    return this.getData('micActive')
  }
}
