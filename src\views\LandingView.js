import { BaseView } from './BaseView.js'

export class LandingView extends BaseView {
  constructor(container) {
    super(container)
    this.onMicClick = null
  }

  render() {
    this.element = this.createElement('div', 'landing-view')
    
    // Main content container
    const content = this.createElement('div', 'landing-content')
    
    // Main text
    const mainText = this.createElement('h1', 'landing-title', 
      'Wanna test how good your speaking skill is?')
    
    // Mic button
    const micButton = this.createElement('button', 'mic-button')
    micButton.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
      </svg>
    `
    
    // Add click event
    micButton.addEventListener('click', () => {
      if (this.onMicClick) {
        this.onMicClick()
      }
    })
    
    // Assemble the view
    content.appendChild(mainText)
    content.appendChild(micButton)
    this.element.appendChild(content)
    
    // Add to container
    this.container.innerHTML = ''
    this.container.appendChild(this.element)
    
    return this.element
  }

  setMicClickHandler(handler) {
    this.onMicClick = handler
  }
}
