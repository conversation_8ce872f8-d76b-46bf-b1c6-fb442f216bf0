/* View Transition API Styles */

/* Landing title transition - fade out upward */
.landing-title {
  view-transition-name: landing-title;
}

::view-transition-old(landing-title) {
  animation: fade-out-up 0.5s ease-in forwards;
}

::view-transition-new(landing-title) {
  animation: none;
}

/* Test passage transition - fade in from bottom */
.test-passage {
  view-transition-name: test-passage;
}

::view-transition-old(test-passage) {
  animation: none;
}

::view-transition-new(test-passage) {
  animation: fade-in-up 0.5s ease-out forwards;
  animation-delay: 0.2s;
}

/* Keyframe animations */
@keyframes fade-out-up {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-30px);
  }
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom root transition for smooth page changes */
::view-transition-old(root) {
  animation: fade-out 0.4s ease-in-out forwards;
}

::view-transition-new(root) {
  animation: fade-in 0.4s ease-in-out forwards;
  animation-delay: 0.2s;
}

/* Mic button transition - keep it smooth */
.mic-button {
  view-transition-name: mic-button;
}

::view-transition-old(mic-button),
::view-transition-new(mic-button) {
  animation: none;
}

/* Additional fade animations */
@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
