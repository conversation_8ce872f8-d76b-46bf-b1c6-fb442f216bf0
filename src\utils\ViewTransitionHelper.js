// Helper utility for View Transition API
export class ViewTransitionHelper {
  static isSupported() {
    return 'startViewTransition' in document
  }

  static transition(callback) {
    if (this.isSupported()) {
      return document.startViewTransition(callback)
    } else {
      // Fallback: just execute the callback
      callback()
      return Promise.resolve()
    }
  }

  static log(message) {
    if (this.isSupported()) {
      console.log('🎬 View Transition:', message)
    } else {
      console.log('⚠️ View Transition not supported:', message)
    }
  }
}
