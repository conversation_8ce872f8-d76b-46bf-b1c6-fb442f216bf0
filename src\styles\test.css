/* Test View Styles */
.test-view {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background);
  position: relative;
}

.test-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding: 4rem 2rem 1rem;
}

.test-passage {
  font-size: 2.5rem;
  line-height: 1.6;
  color: var(--text-primary);
  max-width: 1000px;
  text-align: center;
  margin-top: 10rem;
}

.mic-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 2rem 5rem;
  margin-bottom: 10rem;
}
