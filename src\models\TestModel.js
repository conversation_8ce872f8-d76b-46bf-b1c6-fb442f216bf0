import { BaseModel } from './BaseModel.js'

export class TestModel extends BaseModel {
  constructor() {
    super()
    this.setData('passage', 'I went to the store to buy some groceries. The store was busy, and there was a long line at the checkout. I still managed to get everything I needed before going home.')
    this.setData('isRecording', false)
    this.setData('recordingResult', null)
  }

  startRecording() {
    this.setData('isRecording', true)
  }

  stopRecording() {
    this.setData('isRecording', false)
  }

  setRecordingResult(result) {
    this.setData('recordingResult', result)
  }

  getPassage() {
    return this.getData('passage')
  }

  isRecording() {
    return this.getData('isRecording')
  }

  getRecordingResult() {
    return this.getData('recordingResult')
  }
}
