import { HashRouter } from './utils/HashRouter.js'
import { LandingPresenter } from './presenters/LandingPresenter.js'
import { TestPresenter } from './presenters/TestPresenter.js'

export class App {
  constructor() {
    this.router = new HashRouter()
    this.currentPresenter = null
    this.appElement = document.querySelector('#app')
  }

  init() {
    this.setupRoutes()
    this.router.init()
  }

  setupRoutes() {
    this.router.addRoute('#/', () => this.showLanding())
    this.router.addRoute('#/test', () => this.showTest())
    
    // Default route
    this.router.setDefaultRoute('#/')
  }

  showLanding() {
    this.cleanupCurrentPresenter()
    this.currentPresenter = new LandingPresenter(this.appElement, this.router)
    this.currentPresenter.init()
  }

  showTest() {
    this.cleanupCurrentPresenter()
    this.currentPresenter = new TestPresenter(this.appElement, this.router)
    this.currentPresenter.init()
  }

  // Helper method to navigate with view transitions
  navigateWithTransition(routeHandler) {
    if (document.startViewTransition) {
      document.startViewTransition(() => {
        routeHandler()
      })
    } else {
      routeHandler()
    }
  }

  cleanupCurrentPresenter() {
    if (this.currentPresenter && this.currentPresenter.destroy) {
      this.currentPresenter.destroy()
    }
    this.currentPresenter = null
  }
}
