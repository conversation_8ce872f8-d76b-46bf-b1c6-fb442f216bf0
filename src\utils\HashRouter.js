export class HashRouter {
  constructor() {
    this.routes = new Map()
    this.defaultRoute = '#/'
  }

  addRoute(hash, handler) {
    this.routes.set(hash, handler)
  }

  setDefaultRoute(hash) {
    this.defaultRoute = hash
  }

  init() {
    // Handle initial load
    this.handleRoute()
    
    // Handle hash changes
    window.addEventListener('hashchange', () => this.handleRoute())
  }

  navigate(hash) {
    window.location.hash = hash
  }

  // Navigate with view transition support
  navigateWithTransition(hash) {
    if (document.startViewTransition && 'startViewTransition' in document) {
      document.startViewTransition(() => {
        this.performNavigation(hash)
      })
    } else {
      this.performNavigation(hash)
    }
  }

  // Perform the actual navigation and route handling
  performNavigation(hash) {
    window.location.hash = hash
    // Manually trigger route handling for immediate transition
    this.handleRoute()
  }

  handleRoute() {
    const hash = window.location.hash || this.defaultRoute
    const handler = this.routes.get(hash)

    if (handler) {
      handler()
    } else {
      // Navigate to default route if route not found
      this.navigate(this.defaultRoute)
    }
  }

  getCurrentRoute() {
    return window.location.hash || this.defaultRoute
  }
}
