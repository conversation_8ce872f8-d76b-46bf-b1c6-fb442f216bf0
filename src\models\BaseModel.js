export class BaseModel {
  constructor() {
    this.data = {}
    this.observers = []
  }

  addObserver(observer) {
    this.observers.push(observer)
  }

  removeObserver(observer) {
    const index = this.observers.indexOf(observer)
    if (index > -1) {
      this.observers.splice(index, 1)
    }
  }

  notifyObservers(data) {
    this.observers.forEach(observer => {
      if (typeof observer.update === 'function') {
        observer.update(data)
      }
    })
  }

  setData(key, value) {
    this.data[key] = value
    this.notifyObservers({ key, value, data: this.data })
  }

  getData(key) {
    return key ? this.data[key] : this.data
  }
}
