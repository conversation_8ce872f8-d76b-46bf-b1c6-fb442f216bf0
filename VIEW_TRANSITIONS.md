# View Transition API Implementation

## Overview
Implementasi View Transition API untuk transisi halus dari landing page ke test page ketika user mengklik tombol microphone.

## Fitur Transisi
- **Landing Title**: Fade out ke arah atas (fade-out-up)
- **Test Passage**: Fade in dari bawah (fade-in-up) 
- **Timing**: Landing title menghilang terlebih dahulu, kemudian test passage muncul dengan delay

## File yang Dimodifikasi

### 1. `src/styles/transitions.css` (NEW)
- Definisi view-transition-name untuk elemen
- Keyframe animations untuk fade-out-up dan fade-in-up
- Timing dan easing untuk transisi yang smooth

### 2. `src/presenters/LandingPresenter.js`
- Import ViewTransitionHelper
- Modifikasi handleMicClick() untuk menggunakan View Transition API
- Logging untuk debugging

### 3. `src/utils/ViewTransitionHelper.js` (NEW)
- Helper utility untuk View Transition API
- Feature detection untuk browser support
- Fallback untuk browser yang tidak support

### 4. `src/utils/HashRouter.js`
- Tambahan method navigateWithTransition()
- Support untuk View Transition API dalam routing

### 5. `index.html`
- Tambahan meta tag untuk View Transition API support

### 6. `src/styles/main.css`
- Import transitions.css

## Browser Support
- **Chrome/Edge**: Full support (experimental)
- **Firefox/Safari**: Fallback ke navigasi normal
- **Feature Detection**: Otomatis menggunakan fallback jika tidak support

## Cara Kerja
1. User klik tombol microphone di landing page
2. ViewTransitionHelper.transition() dipanggil
3. Browser capture snapshot dari current state
4. Router navigate ke test page
5. Browser render new state
6. CSS animations dijalankan:
   - Landing title fade out upward (0.5s)
   - Test passage fade in from bottom (0.5s + 0.2s delay)

## Testing
1. Buka http://localhost:5173/
2. Klik tombol microphone
3. Perhatikan transisi smooth dari landing ke test page
4. Check browser console untuk log messages

## Notes
- View Transition API masih experimental
- Perlu enable flags di Chrome untuk testing
- Fallback otomatis untuk browser yang tidak support
