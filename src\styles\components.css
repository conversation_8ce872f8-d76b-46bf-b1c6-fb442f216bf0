/* <PERSON><PERSON> Styles */
.mic-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #6B7280;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 20px rgba(107, 114, 128, 0.3);
  position: relative;
  overflow: hidden;
}

.mic-button:hover {
  transform: translateY(-2px);
  background: #4B5563;
  box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
}

.mic-button:active {
  transform: translateY(0);
  background: #374151;
  box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
}

.test-mic {
  width: 100px;
  height: 100px;
}

.mic-button svg {
  width: 32px;
  height: 32px;
}

.test-mic svg {
  width: 40px;
  height: 40px;
}
